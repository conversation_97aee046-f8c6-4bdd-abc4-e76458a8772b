import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/contexts/AuthContext';
import { Chrome, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

export function AuthTest() {
  const [testResults, setTestResults] = useState<Record<string, boolean>>({});
  const [loading, setLoading] = useState(false);
  const { user, session, signInWithProvider, signOut } = useAuth();

  const runTests = async () => {
    setLoading(true);
    const results: Record<string, boolean> = {};

    try {
      // Test 1: Check Supabase client initialization
      results['client_init'] = !!supabase;

      // Test 2: Check auth configuration
      const { data: { session: currentSession } } = await supabase.auth.getSession();
      results['session_check'] = true;

      // Test 3: Check database connection
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      results['db_connection'] = !error;

      // Test 4: Check auth state
      results['auth_state'] = !!user || !!session;

      // Test 5: Check Google OAuth configuration
      try {
        // This will fail if Google OAuth is not configured
        const { error: oauthError } = await supabase.auth.signInWithOAuth({
          provider: 'google',
          options: {
            redirectTo: `${window.location.origin}/auth-test`,
            skipBrowserRedirect: true
          }
        });
        results['google_oauth_config'] = !oauthError;
      } catch (err) {
        results['google_oauth_config'] = false;
      }

      setTestResults(results);
    } catch (error) {
      console.error('Test error:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    try {
      const { error } = await signInWithProvider('google');
      if (error) {
        console.error('Google login error:', error);
      }
    } catch (err) {
      console.error('Unexpected error:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    await signOut();
  };

  useEffect(() => {
    runTests();
  }, []);

  const getStatusIcon = (status: boolean | undefined) => {
    if (status === undefined) return <AlertCircle className="w-4 h-4 text-yellow-500" />;
    return status ? <CheckCircle className="w-4 h-4 text-green-500" /> : <XCircle className="w-4 h-4 text-red-500" />;
  };

  const getStatusBadge = (status: boolean | undefined) => {
    if (status === undefined) return <Badge variant="secondary">Testing...</Badge>;
    return status ? <Badge variant="default">Pass</Badge> : <Badge variant="destructive">Fail</Badge>;
  };

  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>Authentication System Test</CardTitle>
          <CardDescription>
            Test the Google OAuth authentication system and diagnose any issues
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current Auth State */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Current Authentication State</h3>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-muted-foreground">User Status:</p>
                <p className="font-medium">{user ? `Logged in as ${user.email}` : 'Not logged in'}</p>
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Session Status:</p>
                <p className="font-medium">{session ? 'Active session' : 'No session'}</p>
              </div>
            </div>
          </div>

          {/* System Tests */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">System Tests</h3>
            <div className="space-y-2">
              {[
                { key: 'client_init', label: 'Supabase Client Initialization' },
                { key: 'session_check', label: 'Session Management' },
                { key: 'db_connection', label: 'Database Connection' },
                { key: 'auth_state', label: 'Authentication State' },
                { key: 'google_oauth_config', label: 'Google OAuth Configuration' }
              ].map(({ key, label }) => (
                <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(testResults[key])}
                    <span>{label}</span>
                  </div>
                  {getStatusBadge(testResults[key])}
                </div>
              ))}
            </div>
          </div>

          {/* Actions */}
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Test Actions</h3>
            <div className="flex space-x-4">
              <Button onClick={runTests} disabled={loading}>
                {loading ? 'Running Tests...' : 'Run Tests'}
              </Button>
              
              {!user && (
                <Button onClick={handleGoogleLogin} disabled={loading} className="flex items-center space-x-2">
                  <Chrome className="w-4 h-4" />
                  <span>Test Google Login</span>
                </Button>
              )}
              
              {user && (
                <Button onClick={handleSignOut} variant="outline">
                  Sign Out
                </Button>
              )}
            </div>
          </div>

          {/* Debug Information */}
          {user && (
            <div className="space-y-2">
              <h3 className="text-lg font-semibold">Debug Information</h3>
              <div className="bg-muted p-4 rounded-lg">
                <pre className="text-sm overflow-auto">
                  {JSON.stringify({
                    user: {
                      id: user.id,
                      email: user.email,
                      provider: user.app_metadata?.provider,
                      created_at: user.created_at
                    },
                    session: session ? {
                      access_token: session.access_token ? 'Present' : 'Missing',
                      refresh_token: session.refresh_token ? 'Present' : 'Missing',
                      expires_at: session.expires_at
                    } : null
                  }, null, 2)}
                </pre>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
