import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { HeaderVisibilityProvider } from "./contexts/HeaderVisibilityContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { ProfileSetup } from "@/components/profile/ProfileSetup";
import DashboardLayout from "@/layouts/DashboardLayout";
import Landing from "./routes/Landing";
import Onboarding from "./routes/Onboarding";
import Topics from "./pages/Topics";
import Dashboard from "./pages/Dashboard";
import Knowledge from "./routes/Knowledge";
import AvatarLayout from "./routes/AvatarLayout";
import Avatar from "./routes/Avatar";
import AvatarCreate from "./routes/AvatarCreate";
import MyAvatar from "./routes/MyAvatar";
import AvatarIcon from "./routes/AvatarIcon";
import AvatarRoomSelection from "./routes/AvatarRoomSelection";
import BioLink from "./routes/BioLink";
import UserBioLink from "./routes/UserBioLink";
import MyAccount from "./routes/MyAccount";
import Upgrade from "./routes/Upgrade";
import TokenRewards from "./routes/TokenRewards";
import AccountSettings from "./routes/AccountSettings";
import PaymentSettings from "./routes/PaymentSettings";
import Wallet from "./pages/Wallet";
import Index from "./pages/Index";
import Login from "./pages/Login";
import AuthTest from "./pages/AuthTest";
import NotFound from "./pages/NotFound";
import { useRTL } from "./hooks/useRTL";

// Initialize i18n
import '@/i18n/config';

const queryClient = new QueryClient();

const App = () => {
  useRTL();
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <HeaderVisibilityProvider>
              <Routes>
                {/* Public Routes */}
                <Route path="/" element={<Landing />} />
                <Route path="/login" element={<Login />} />
                <Route path="/auth-test" element={<AuthTest />} />
                <Route path="/index" element={<Index />} />
                <Route path="/onboarding" element={<Onboarding />} />

                {/* Profile Setup */}
                <Route 
                  path="/setup" 
                  element={
                    <ProtectedRoute>
                      <ProfileSetup />
                    </ProtectedRoute>
                  } 
                />

                {/* Wallet Route */}
                <Route 
                  path="/wallet" 
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <Wallet />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } 
                />

                {/* Dashboard Layout Routes with Mobile Navigation */}
                <Route 
                  path="/topics" 
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <Topics />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/knowledge" 
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <Knowledge />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } 
                />

                <Route
                  path="/avatar/*"
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <AvatarLayout />
                      </DashboardLayout>
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<Avatar />} />
                  <Route path="create" element={<AvatarCreate />} />
                  <Route path="myavatar" element={<MyAvatar />} />
                  <Route path="icon" element={<AvatarIcon />} />
                  <Route path="room-selection" element={<AvatarRoomSelection />} />
                </Route>

                <Route
                  path="/bio-link"
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <BioLink />
                      </DashboardLayout>
                    </ProtectedRoute>
                  }
                />
                <Route 
                  path="/dashboard" 
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <Dashboard />
                      </DashboardLayout>
                    </ProtectedRoute>
                  } 
                />

                {/* Account Routes with Dashboard Layout */}
                <Route
                  path="/my-account"
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <MyAccount />
                      </DashboardLayout>
                    </ProtectedRoute>
                  }
                >
                  <Route index element={<Navigate to="account" replace />} />
                  <Route path="account" element={<AccountSettings />} />
                  <Route path="payment" element={<PaymentSettings />} />
                  <Route path="upgrade" element={<Upgrade />} />
                  <Route path="token-rewards" element={<TokenRewards />} />
                </Route>

                {/* Rewards Routes with Dashboard Layout */}
                <Route
                  path="/rewards"
                  element={
                    <ProtectedRoute>
                      <DashboardLayout>
                        <TokenRewards />
                      </DashboardLayout>
                    </ProtectedRoute>
                  }
                />

                {/* Standalone Routes */}
                <Route 
                  path="/upgrade" 
                  element={
                    <ProtectedRoute>
                      <Upgrade />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/settings" 
                  element={
                    <ProtectedRoute>
                      <AccountSettings />
                    </ProtectedRoute>
                  } 
                />
                <Route 
                  path="/payments" 
                  element={
                    <ProtectedRoute>
                      <PaymentSettings />
                    </ProtectedRoute>
                  } 
                />

                {/* Catch all - 404 */}
                <Route path="*" element={<NotFound />} />
              </Routes>
            </HeaderVisibilityProvider>
          </BrowserRouter>
        </TooltipProvider>
      </AuthProvider>
    </QueryClientProvider>
  );
};

export default App;
