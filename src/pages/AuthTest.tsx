import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { debugAuthState, clearAuthState } from '@/utils/authDebug';

export default function AuthTest() {
  const { user, session, loading, signUp, signIn } = useAuth();
  const [testResults, setTestResults] = useState<any[]>([]);
  const [testEmail, setTestEmail] = useState('<EMAIL>');
  const [testPassword, setTestPassword] = useState('testpassword123');

  const addTestResult = (test: string, result: any, success: boolean) => {
    setTestResults(prev => [...prev, { test, result, success, timestamp: new Date() }]);
  };

  const testSignUp = async () => {
    try {
      const result = await signUp(testEmail, testPassword);
      addTestResult('Sign Up Test', result, !result.error);
    } catch (error) {
      addTestResult('Sign Up Test', { error }, false);
    }
  };

  const testSignIn = async () => {
    try {
      const result = await signIn(testEmail, testPassword);
      addTestResult('Sign In Test', result, !result.error);
    } catch (error) {
      addTestResult('Sign In Test', { error }, false);
    }
  };

  const runTests = async () => {
    setTestResults([]);

    // Test 1: Check current auth state
    try {
      const { data: { session }, error } = await supabase.auth.getSession();
      addTestResult('Get Session', { session: !!session, error }, !error);
    } catch (error) {
      addTestResult('Get Session', { error }, false);
    }

    // Test 2: Test database connection
    try {
      const { data, error } = await supabase.from('profiles').select('count').limit(1);
      addTestResult('Database Connection', { data, error }, !error);
    } catch (error) {
      addTestResult('Database Connection', { error }, false);
    }

    // Test 3: Test avatars table
    try {
      const { data, error } = await supabase.from('avatars').select('count').limit(1);
      addTestResult('Avatars Table Access', { data, error }, !error);
    } catch (error) {
      addTestResult('Avatars Table Access', { error }, false);
    }

    // Test 4: Test user profile if logged in
    if (user) {
      try {
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
        addTestResult('User Profile', { data: !!data, error }, !error);
      } catch (error) {
        addTestResult('User Profile', { error }, false);
      }
    }
  };

  useEffect(() => {
    if (!loading) {
      runTests();
    }
  }, [loading, user]);

  return (
    <div className="min-h-screen p-8 bg-background">
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Test Page</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold">Auth State</h3>
                <p>Loading: {loading ? 'Yes' : 'No'}</p>
                <p>User: {user ? user.email : 'Not logged in'}</p>
                <p>Session: {session ? 'Active' : 'None'}</p>
              </div>
              <div>
                <h3 className="font-semibold">Environment</h3>
                <p>Supabase URL: {import.meta.env.VITE_SUPABASE_URL || 'Not set'}</p>
                <p>RPM Subdomain: {import.meta.env.VITE_READY_PLAYER_ME_SUBDOMAIN || 'Not set'}</p>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex gap-2">
                <Button onClick={runTests}>Run Tests</Button>
                <Button onClick={debugAuthState} variant="outline">Debug Auth</Button>
                <Button onClick={clearAuthState} variant="destructive">Clear Auth</Button>
              </div>

              <div className="border-t pt-4">
                <h3 className="font-semibold mb-2">Test Authentication</h3>
                <div className="flex gap-2 mb-2">
                  <input
                    type="email"
                    value={testEmail}
                    onChange={(e) => setTestEmail(e.target.value)}
                    placeholder="Test email"
                    className="px-3 py-1 border rounded"
                  />
                  <input
                    type="password"
                    value={testPassword}
                    onChange={(e) => setTestPassword(e.target.value)}
                    placeholder="Test password"
                    className="px-3 py-1 border rounded"
                  />
                </div>
                <div className="flex gap-2">
                  <Button onClick={testSignUp} size="sm">Test Sign Up</Button>
                  <Button onClick={testSignIn} size="sm" variant="outline">Test Sign In</Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-3 rounded border ${
                    result.success ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'
                  }`}
                >
                  <div className="flex justify-between items-start">
                    <span className="font-medium">{result.test}</span>
                    <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                      {result.success ? '✓' : '✗'}
                    </span>
                  </div>
                  <pre className="text-xs mt-2 overflow-auto">
                    {JSON.stringify(result.result, null, 2)}
                  </pre>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
